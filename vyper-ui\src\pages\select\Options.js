import { Form<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>rid, Switch } from "@material-ui/core";
import Button from "@material-ui/core/Button";
import { makeStyles } from "@material-ui/core/styles";
import React, { useContext, useEffect, useState } from "react";
import { ApprovalOperationContext } from "../../component/approvaloperation/ApprovalOperation";
import { WorkFlowButton } from "./../mockup/workflow/WorkFlowButton";
import {
  checkIsInGroup,
  NON_VALIDATABLE_OPERATIONS,
} from "./traveler/ValidatedOperation";
import { FillComponentWidget } from "src/pages/select/FillComponentWidget";

const useStyles = makeStyles({
  root: {
    padding: "10px",
    paddingLeft: "1rem",
    borderLeft: "none",
    borderRight: "none",
    backgroundColor: "hsla(0, 100%, 95%, 1)",
    display: "flex",
    justifyContent: "center",
  },
  dragDropButtonContainer: {
    display: "flex",
    gap: "1em",
    justifyContent: "center",
    padding: "1em",
    backgroundColor: "#f5f5f5",
    borderRadius: "4px",
    border: "1px solid #ddd",
  },
});

/**
 *
 * @param {object} build - The build
 * @param {object} options - The options array
 * @param {function} onToggleAttribute -
 * @param {function} onToggleParagraph -
 * @param {function} onToggleComponent -
 * @param {function} onToggleHeader -
 * @param {function} onToggleEditButton -
 * @param {function} onToggleRejection -
 * @param {function} onToggleComments -
 * @param {function} onToggleChangelog -
 * @param {function} onToggleDragDrop -
 * @param {function} onClickFillUnselected -
 * @param {function} onClickApprove -
 * @param {function} onClickRework -
 * @param isApprover
 * @param isFrozen
 * @param handleAddComment
 * @param dragDropChanges
 * @param onDragDropSave
 * @param onDragDropCancel
 * @returns {React.ReactNode}
 * @constructor
 */
const Options = ({
  build,
  options,
  onToggleAttribute,
  onToggleParagraph,
  onToggleComponent,
  onToggleHeader,
  onToggleEditButton,
  onToggleRejection,
  onToggleComments,
  onToggleChangelog,
  onToggleDragDrop,
  onClickApprove,
  onClickRework,
  isApprover,
  isFrozen,
  onAddComment,
  onRefreshAttributes,
  onClickFillUnselectedAtss,
  onClickFillUnselectedVyper,
  onClickFillUnselectedClear,
  enableUnselectedClear,
  dragDropChanges,
  onDragDropSave,
  onDragDropCancel,
}) => {
  const { findApprovalOperationByOperation } = useContext(
    ApprovalOperationContext
  );

  const enableShowParagraph = options.component === true;
  const enableShowAttribute = options.component === true;
  const enableEditButton = options.dragdrop === true;

  const [isValidated, setIsValidated] = useState(false);
  const [, setValSelected] = useState("");

  useEffect(() => {
    setValSelected("");
    setIsValidated("");
    setIsValidated([""]);
  }, []);

  const unValidatedOperations = () => {
    let grps;
    if (validationGroups != null && validationGroups.length > 0) {
      grps = validationGroups;
    } else {
      grps = isValidated;
    }

    const groups = grps
      .map((role) => role.groupName)
      .filter((groupName) => groupName != null)
      .map((groupName) => groupName.toLowerCase());

    // grab some data from the build
    const buildFacility = build.facility?.object?.PDBFacility;
    const buildSbe = build.material?.object?.SBE;
    const buildSbe1 = build.material?.object?.SBE1;

    return (
      build.traveler.operations
        .map((operation) => operation.name)

        // filter out operations that do not need to be approved by the current user
        .filter((operation) => {
          const ao = findApprovalOperationByOperation(operation);
          return groups.some((group) => {
            return checkIsInGroup(
              group,
              ao,
              buildFacility,
              buildSbe,
              buildSbe1
            );
          });
        })

        // filter out operations that are already validated
        .filter((operation) => {
          const vo = build.validatedOperations.find(
            (v) => operation.toLowerCase() === v.operation.toLowerCase()
          );
          return vo?.when == null;
        })

        // return the name of the operation
        .map((operation) => operation.name)

        // filter out the non-validatable operations
        .filter((operation) => !NON_VALIDATABLE_OPERATIONS.includes(operation))
    );
  };

  const atApproveBlockers = () => {
    const blockers = [];

    if (isFrozen) {
      blockers.push("This group has already approved or reworked.");
    }
    unValidatedOperations().forEach((o) =>
      blockers.push(`The operation is not checked: ${o}.`)
    );
    return blockers;
  };

  const showApprove =
    isApprover &&
    !isFrozen &&
    (build.state === "AT_REVIEW_CHANGE" || build.state === "REWORK");
  const showRework =
    ((build.state === "AT_REVIEW_CHANGE" || build.state === "REWORK") &&
      !isFrozen &&
      isApprover) ||
    build.state === "BU_REVIEW_CHANGE";

  const classes = useStyles();

  // noinspection PointlessBooleanExpressionJS
  return options == null ? null : (
    <div className={classes.root}>
      <form>
        <Grid container spacing={2}>
          <Grid item xs={12} md={5}>
            <FormControlLabel
              control={
                <Switch
                  size="small"
                  checked={options.header}
                  onChange={onToggleHeader}
                  name="show-header"
                  color="primary"
                />
              }
              label="Show Header"
            />
            <FormControlLabel
              control={
                <Switch
                  size="small"
                  checked={options.component}
                  onChange={onToggleComponent}
                  name="show-components"
                  color="primary"
                />
              }
              label="Show Components"
            />
            <FormControlLabel
              control={
                <Switch
                  size="small"
                  checked={options.paragraph}
                  onChange={onToggleParagraph}
                  name="show-paragraphs"
                  color="primary"
                />
              }
              label="Show Paragraphs"
              disabled={!enableShowParagraph}
            />
            <FormControlLabel
              control={
                <Switch
                  size="small"
                  checked={options.attribute}
                  onChange={onToggleAttribute}
                  name="show-attributes"
                  color="primary"
                />
              }
              label="Show Attributes"
              disabled={!enableShowAttribute}
            />
            <FormControlLabel
              control={
                <Switch
                  size="small"
                  checked={options.editbutton}
                  onChange={onToggleEditButton}
                  name="edit-buttons"
                  color="primary"
                />
              }
              label="Show Edit Buttons"
              disabled={enableEditButton}
            />
            <FormControlLabel
              control={
                <Switch
                  size="small"
                  checked={options.rejection}
                  onChange={onToggleRejection}
                  name="show-rejection"
                  color="primary"
                />
              }
              label="Show Rejection Values"
            />
            <FormControlLabel
              control={
                <Switch
                  size="small"
                  checked={options.comments}
                  onChange={onToggleComments}
                  name="show-comments"
                  color="primary"
                />
              }
              label="Show Comments"
            />
            <FormControlLabel
              control={
                <Switch
                  size="small"
                  checked={options.changelog}
                  onChange={onToggleChangelog}
                  name="show-changelog"
                  color="primary"
                />
              }
              label="Show Changelog"
            />
            <FormControlLabel
              control={
                <Switch
                  size="small"
                  checked={options.dragdrop}
                  onChange={onToggleDragDrop}
                  name="drag-drop"
                  color="primary"
                />
              }
              label="Drag & Drop"
            />
          </Grid>

          <Grid item xs={12} md={3}>
            <FillComponentWidget
              onClickAtss={onClickFillUnselectedAtss}
              onClickVyper={onClickFillUnselectedVyper}
              onClickClear={onClickFillUnselectedClear}
              enableClear={enableUnselectedClear}
              fillComponent={build.fillComponent}
            />
          </Grid>

          <Grid item xs={6} md={2}>
             <div
              style={{
                display: "flex",
                flexDirection: "column",
                gap: "1em",
                maxWidth: "10em",
              }}
            >
              {showApprove ? (
                <WorkFlowButton
                  visible={true}
                  blockers={atApproveBlockers}
                  value="Approve"
                  onClick={onClickApprove}
                />
              ) : null}
              {showRework ? (
                <WorkFlowButton
                  visible={true}
                  blockers={[]}
                  value="REWORK"
                  onClick={onClickRework}
                />
              ) : null}
            </div>
          </Grid>
          <Grid item xs={6} md={2}>
          <div
              style={{ display: "flex", flexDirection: "column", gap: "1em" }}
            >
            <Button
              color="primary"
              variant="contained"
              size="small"
              onClick={onAddComment}
            >
              Comments
            </Button>

            <Button
                color="primary"
                variant="contained"
                size="small"
                onClick={onRefreshAttributes}
              >
                Refresh Attributes
              </Button>
            </div>
          </Grid>

          {/* Drag and Drop Save/Cancel buttons */}
          {options.dragdrop && dragDropChanges?.hasChanges && (
            <Grid item xs={12}>
              <div className={classes.dragDropButtonContainer}>
                <Button
                  color="primary"
                  variant="contained"
                  size="small"
                  onClick={onDragDropSave}
                >
                  Save Changes
                </Button>
                <Button
                  color="secondary"
                  variant="outlined"
                  size="small"
                  onClick={onDragDropCancel}
                >
                  Cancel
                </Button>
              </div>
            </Grid>
          )}
        </Grid>
      </form>
    </div>
  );
};

export default Options;
